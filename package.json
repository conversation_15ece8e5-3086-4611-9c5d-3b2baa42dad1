{"name": "redis-sessions", "description": "An advanced session store for Redis", "version": "4.0.0", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "engines": {"node": ">=16.0.0"}, "scripts": {"prepare": "npm run build", "prebuild": "npm run lint", "build": "npm run build:ts", "build:ts": "tsc --declaration", "test": "node node_modules/.bin/mocha ./dist/test/test.js", "prepublishOnly": "npm run lint", "preversion": "npm run lint", "postversion": "echo 'IMPORTANT: Run `git push && git push --tags` to push created version to repo and `npm publish` to publish to npm'", "lint": "eslint", "watch": "tsc -w"}, "dependencies": {"lodash": "^4.17.21", "lru-cache": "^10.1.0", "redis": "^4.6.11"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/mocha": "^10.0.6", "@types/node": "^20.11.5", "@typescript-eslint/eslint-plugin": "^6.5.0", "eslint": "8.52.0", "eslint-plugin-jsdoc": "^46.5.1", "eslint-plugin-unicorn": "^49.0.0", "mocha": "^10.2.0", "should": "13.2.3", "typescript": "^5.2.2", "typescript-eslint": "^0.0.1-alpha.0"}, "keywords": ["sessions", "redis", "session", "users", "activity"], "repository": {"type": "git", "url": "http://github.com/smrchy/redis-sessions.git"}, "mocha": {"bail": true, "slow": 5, "timeout": 8000, "reporter": "spec"}}